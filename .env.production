# Go API Solve 生产环境配置
# 基于您提供的实际配置信息

# ===========================================
# 服务器配置
# ===========================================
SERVER_PORT=8080
GIN_MODE=release

# ===========================================
# MySQL 数据库配置 (您的实际配置)
# ===========================================
MYSQL_HOST=***********
MYSQL_PORT=3380
MYSQL_USERNAME=gmdns
MYSQL_PASSWORD=5e7fFn3HpPfuQ6Qx42Az
MYSQL_DATABASE=solve_api_go
MYSQL_CHARSET=utf8mb4

# ===========================================
# Redis 配置 (您的实际配置)
# ===========================================
REDIS_HOST=r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com
REDIS_PORT=6379
REDIS_USERNAME=r-bp1t323p6w8yn2cpq0
REDIS_PASSWORD=EtdDj8xJ385pUPUT
REDIS_DB=0

# ===========================================
# AI 模型 API 密钥配置 (您的实际配置)
# ===========================================
# 通义千问 API Key
QWEN_KEY=sk-3920274bedf642c2b7495f534aadca84

# DeepSeek API Key  
DEEPSEEK_KEY=***********************************

# ===========================================
# 注意事项
# ===========================================
# 1. 此文件包含您的实际生产配置
# 2. 请确保服务器能访问外部数据库和Redis
# 3. 请确保API密钥有效且有足够额度
# 4. 在宝塔面板中，将此文件重命名为 .env 使用
