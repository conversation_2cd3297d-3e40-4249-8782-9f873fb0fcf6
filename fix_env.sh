#!/bin/bash

# Go API Solve - 环境变量修复脚本
# 解决数据库连接问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

APP_NAME="go-api-solve"
APP_DIR="/www/wwwroot/$APP_NAME"

echo -e "${BLUE}"
echo "=================================================="
echo "    Go API Solve 环境变量修复脚本"
echo "=================================================="
echo -e "${NC}"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    print_error "请使用root用户运行此脚本"
    exit 1
fi

# 检查应用目录
if [ ! -d "$APP_DIR" ]; then
    print_error "应用目录不存在: $APP_DIR"
    print_info "请先运行安装脚本"
    exit 1
fi

# 停止服务
print_info "停止服务..."
systemctl stop $APP_NAME 2>/dev/null || true

# 检查当前配置
print_info "检查当前配置..."
if [ -f "$APP_DIR/.env" ]; then
    print_warning "发现现有 .env 文件，备份中..."
    cp "$APP_DIR/.env" "$APP_DIR/.env.backup.$(date +%Y%m%d_%H%M%S)"
fi

# 创建正确的环境变量文件
print_info "创建正确的环境变量文件..."
cat > "$APP_DIR/.env" << 'EOF'
# Go API Solve 生产环境配置
SERVER_PORT=8080
GIN_MODE=release

# MySQL 数据库配置
MYSQL_HOST=***********
MYSQL_PORT=3380
MYSQL_USERNAME=gmdns
MYSQL_PASSWORD=5e7fFn3HpPfuQ6Qx42Az
MYSQL_DATABASE=solve_api_go
MYSQL_CHARSET=utf8mb4

# Redis 配置
REDIS_HOST=r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com
REDIS_PORT=6379
REDIS_USERNAME=r-bp1t323p6w8yn2cpq0
REDIS_PASSWORD=EtdDj8xJ385pUPUT
REDIS_DB=0

# AI 模型 API 密钥配置
QWEN_KEY=sk-3920274bedf642c2b7495f534aadca84
DEEPSEEK_KEY=***********************************
EOF

# 设置正确的权限
print_info "设置文件权限..."
chown www:www "$APP_DIR/.env"
chmod 600 "$APP_DIR/.env"

# 验证环境变量文件
print_info "验证环境变量文件..."
if [ -f "$APP_DIR/.env" ]; then
    print_success "环境变量文件创建成功"
    echo "文件内容预览:"
    echo "----------------------------------------"
    head -10 "$APP_DIR/.env"
    echo "----------------------------------------"
else
    print_error "环境变量文件创建失败"
    exit 1
fi

# 更新systemd服务文件
print_info "更新systemd服务文件..."
if [ -f "./go-api-solve.service" ]; then
    cp ./go-api-solve.service /etc/systemd/system/
    systemctl daemon-reload
    print_success "systemd服务文件已更新"
else
    print_warning "未找到新的服务文件，使用现有配置"
fi

# 测试数据库连接
print_info "测试数据库连接..."
if command -v mysql &> /dev/null; then
    if mysql -h*********** -P3380 -ugmdns -p5e7fFn3HpPfuQ6Qx42Az -e "SELECT 1;" 2>/dev/null; then
        print_success "数据库连接测试成功"
    else
        print_warning "数据库连接测试失败，请检查网络和凭据"
    fi
else
    print_warning "未安装mysql客户端，跳过连接测试"
fi

# 测试Redis连接
print_info "测试Redis连接..."
if command -v redis-cli &> /dev/null; then
    if redis-cli -h r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com -p 6379 -a EtdDj8xJ385pUPUT ping 2>/dev/null | grep -q PONG; then
        print_success "Redis连接测试成功"
    else
        print_warning "Redis连接测试失败，请检查网络和凭据"
    fi
else
    print_warning "未安装redis-cli，跳过连接测试"
fi

# 启动服务
print_info "启动服务..."
systemctl start $APP_NAME

# 等待服务启动
sleep 3

# 检查服务状态
if systemctl is-active --quiet $APP_NAME; then
    print_success "服务启动成功!"
    
    # 测试API
    print_info "测试API..."
    sleep 2
    if curl -s http://localhost:8080/api/v1/health > /dev/null; then
        print_success "API测试成功!"
        echo ""
        echo "🌐 API地址: http://$(hostname -I | awk '{print $1}'):8080/api/v1/health"
    else
        print_warning "API测试失败，请检查日志"
    fi
else
    print_error "服务启动失败!"
    print_info "查看错误日志:"
    journalctl -u $APP_NAME -n 20 --no-pager
    exit 1
fi

echo ""
print_success "环境变量修复完成!"
echo ""
echo "🔧 管理命令:"
echo "查看状态: systemctl status $APP_NAME"
echo "查看日志: journalctl -u $APP_NAME -f"
echo "重启服务: systemctl restart $APP_NAME"
echo ""
echo "📝 配置文件: $APP_DIR/.env"
