[Unit]
Description=Go API Solve Service
Documentation=https://github.com/your-repo/go-api-solve
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=simple
User=www
Group=www
WorkingDirectory=/www/wwwroot/go-api-solve
ExecStart=/www/wwwroot/go-api-solve/go-api-solve
ExecReload=/bin/kill -HUP $MAINPID
KillMode=process
Restart=on-failure
RestartSec=5s
StandardOutput=journal
StandardError=journal
SyslogIdentifier=go-api-solve

# 环境变量文件
EnvironmentFile=-/www/wwwroot/go-api-solve/.env

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/www/wwwroot/go-api-solve

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
