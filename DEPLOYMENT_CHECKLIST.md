# Go API Solve - 宝塔面板部署清单

## 📦 您已获得的完整部署包

### 🎯 部署包文件

**主部署包**: `go-api-solve-baota-deployment.tar.gz` (4.4MB)

包含以下文件：

#### 🔧 可执行文件和配置
- ✅ `deploy/go-api-solve` - Linux x64 可执行文件 (11MB)
- ✅ `deploy/migrations/` - 数据库迁移文件
- ✅ `.env.production` - **您的实际生产配置**
- ✅ `.env.example` - 配置模板
- ✅ `go-api-solve.service` - systemd服务文件

#### 🚀 自动化脚本
- ✅ `install.sh` - 一键安装脚本
- ✅ `deploy.sh` - 部署脚本
- ✅ `build.sh` - 构建脚本
- ✅ `Makefile` - 构建工具

#### 📚 文档
- ✅ `QUICK_DEPLOY.md` - 5分钟快速部署指南
- ✅ `DEPLOYMENT_README.md` - 详细部署说明
- ✅ `docs/BAOTA_DEPLOYMENT.md` - 宝塔面板专用文档

## 🎯 快速部署 (推荐)

### 1️⃣ 上传部署包

将 `go-api-solve-baota-deployment.tar.gz` 上传到您的Linux服务器

### 2️⃣ 解压并安装

```bash
# 解压部署包
tar -xzf go-api-solve-baota-deployment.tar.gz

# 进入目录
cd Go_api_solve/

# 一键安装
sudo ./install.sh
```

### 3️⃣ 使用您的配置

```bash
# 使用您的实际配置
cp .env.production /www/wwwroot/go-api-solve/.env

# 重启服务
systemctl restart go-api-solve
```

### 4️⃣ 验证部署

```bash
# 检查服务状态
systemctl status go-api-solve

# 测试API
curl http://localhost:8080/api/v1/health
```

## 🔧 您的实际配置信息

已包含在 `.env.production` 文件中：

### 数据库配置
- **主机**: ***********:3380
- **用户**: gmdns
- **密码**: 5e7fFn3HpPfuQ6Qx42Az
- **数据库**: solve_api_go

### Redis配置
- **主机**: r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com:6379
- **用户**: r-bp1t323p6w8yn2cpq0
- **密码**: EtdDj8xJ385pUPUT

### API密钥
- **Qwen**: sk-3920274bedf642c2b7495f534aadca84
- **DeepSeek**: ***********************************

## 🌐 部署后访问

### API地址
- **健康检查**: `http://your-server-ip:8080/api/v1/health`
- **图片处理**: `http://your-server-ip:8080/api/v1/process-image`

### 宝塔面板配置
1. 添加网站并设置反向代理到 `http://127.0.0.1:8080`
2. 开放8080端口
3. 可选：配置SSL证书

## 📊 服务管理命令

```bash
# 启动服务
systemctl start go-api-solve

# 停止服务
systemctl stop go-api-solve

# 重启服务
systemctl restart go-api-solve

# 查看状态
systemctl status go-api-solve

# 查看日志
journalctl -u go-api-solve -f
```

## 🔍 故障排除

### 常见问题检查

1. **服务状态**: `systemctl status go-api-solve`
2. **端口占用**: `netstat -tlnp | grep 8080`
3. **文件权限**: `ls -la /www/wwwroot/go-api-solve/`
4. **配置文件**: `cat /www/wwwroot/go-api-solve/.env`
5. **错误日志**: `journalctl -u go-api-solve -n 50`

### 连接测试

```bash
# 测试MySQL连接
mysql -h*********** -P3380 -ugmdns -p solve_api_go

# 测试Redis连接  
redis-cli -h r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com -p 6379 -a EtdDj8xJ385pUPUT ping
```

## ✅ 部署检查清单

- [ ] 上传部署包到服务器
- [ ] 解压并运行安装脚本
- [ ] 复制生产配置文件
- [ ] 检查服务状态
- [ ] 测试API健康检查
- [ ] 配置宝塔面板反向代理
- [ ] 开放防火墙端口
- [ ] 测试完整API功能

## 🎉 部署完成

如果所有步骤都成功，您的Go API Solve服务现在应该在宝塔面板环境中正常运行！

---

**技术支持**: 如遇问题，请提供服务状态和错误日志信息。
