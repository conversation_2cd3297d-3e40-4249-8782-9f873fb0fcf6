-- 创建模型配置表
-- 存储AI模型的配置参数，支持动态配置
CREATE TABLE IF NOT EXISTS quest_model_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    model_name VARCHAR(100) NOT NULL COMMENT '模型名称（qwen-vl-plus, deepseek-chat）',
    role_system TEXT NOT NULL COMMENT 'system角色的Content',
    role_user TEXT NOT NULL COMMENT 'user角色的Content',
    temperature DECIMAL(3,2) DEFAULT 0.00 COMMENT '温度参数，默认值为0',
    top_p DECIMAL(3,2) DEFAULT 0.80 COMMENT 'TopP参数，默认值为0.8，越高文本越多样性',
    top_k INT DEFAULT 50 COMMENT 'TopK参数，默认值为50，候选集越少，稳定性越高',
    repetition_penalty DECIMAL(4,3) DEFAULT 1.000 COMMENT '重复惩罚，OCR建议为1-1.05',
    presence_penalty DECIMAL(4,3) DEFAULT 1.500 COMMENT '存在惩罚，OCR建议1.5',
    response_format VARCHAR(50) DEFAULT 'json_object' COMMENT '返回格式（json_object或text）',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用，1启用，0禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_model_name (model_name) COMMENT '模型名称唯一索引',
    INDEX idx_is_active (is_active) COMMENT '启用状态索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模型配置表';

-- 插入默认的Qwen模型配置
INSERT INTO quest_model_config (
    model_name, 
    role_system, 
    role_user, 
    temperature, 
    top_p, 
    top_k, 
    repetition_penalty, 
    presence_penalty, 
    response_format
) VALUES (
    'qwen-vl-plus',
    '你是一个专业的图片题目识别助手，能够准确识别图片中的题目内容、选项和相关信息。请以JSON格式返回识别结果。',
    '请识别这张图片中的题目内容，包括题目类型、题目文本和所有选项，以JSON格式返回。',
    0.00,
    0.80,
    50,
    1.050,
    1.500,
    'json_object'
);

-- 插入默认的DeepSeek模型配置  
INSERT INTO quest_model_config (
    model_name,
    role_system,
    role_user, 
    temperature,
    top_p,
    top_k,
    repetition_penalty,
    presence_penalty,
    response_format
) VALUES (
    'deepseek-chat',
    '你是一个专业的题目分析助手，能够根据题目内容提供准确的答案和详细的解析。请以JSON格式返回分析结果。',
    '请分析以下题目，提供正确答案和详细解析，以JSON格式返回。',
    1.00,
    1.00,
    50,
    1.000,
    1.500,
    'json_object'
);
