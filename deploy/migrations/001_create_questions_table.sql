-- 创建题目主表
-- 存储题目的完整信息，包括原始数据和解析结果
CREATE TABLE IF NOT EXISTS questions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    cache_key_hash VARCHAR(255) NOT NULL COMMENT '被哈希化的缓存键名字',
    question_type VARCHAR(50) NOT NULL COMMENT '问题类型（单选题、多选题、判断题）',
    question_text TEXT NOT NULL COMMENT '问题内容',
    option_a TEXT COMMENT '问题选项A',
    option_b TEXT COMMENT '问题选项B', 
    option_c TEXT COMMENT '问题选项C',
    option_d TEXT COMMENT '问题选项D',
    option_y TEXT COMMENT '问题选项Y（判断题正确）',
    option_n TEXT COMMENT '问题选项N（判断题错误）',
    answer JSON COMMENT '问题答案（JSON格式）',
    analysis TEXT COMMENT '问题解析',
    user_image VARCHAR(500) COMMENT '问题对应的图片名称',
    image_url VARCHAR(1000) NOT NULL COMMENT '用户提交的图片URL地址',
    qwen_raw JSON COMMENT 'Qwen返回的原始数据',
    deepseek_raw JSON COMMENT 'DeepSeek返回的原始数据',
    qwen_parsed JSON COMMENT '被格式化解析后的Qwen数据',
    is_verified TINYINT DEFAULT 0 COMMENT '是否已经验证过，默认值为0',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_cache_key_hash (cache_key_hash) COMMENT '缓存键哈希索引',
    INDEX idx_question_type (question_type) COMMENT '问题类型索引',
    INDEX idx_created_at (created_at) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题目信息表';
