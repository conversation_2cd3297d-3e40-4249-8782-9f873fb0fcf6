# Go API Solve - 宝塔面板快速部署指南

## 🎯 5分钟快速部署

### 📦 部署包内容

您已经获得了完整的Linux部署包，包含：

- ✅ **Linux可执行文件** (`deploy/go-api-solve`)
- ✅ **数据库迁移文件** (`deploy/migrations/`)
- ✅ **环境变量配置** (`.env.production` - 包含您的实际配置)
- ✅ **系统服务文件** (`go-api-solve.service`)
- ✅ **自动化脚本** (`install.sh`, `deploy.sh`)

### 🚀 快速部署步骤

#### 步骤1: 上传文件到服务器

将以下文件上传到您的Linux服务器：

```bash
# 上传到服务器任意目录，例如 /root/
go-api-solve-deployment.tar.gz
```

#### 步骤2: 解压并进入目录

```bash
cd /root/
tar -xzf go-api-solve-deployment.tar.gz
cd Go_api_solve/  # 或解压后的目录名
```

#### 步骤3: 一键安装

```bash
# 给脚本执行权限
chmod +x install.sh

# 运行一键安装脚本
sudo ./install.sh
```

安装脚本会自动：
- ✅ 创建应用目录 `/www/wwwroot/go-api-solve/`
- ✅ 复制可执行文件和配置
- ✅ 设置正确的文件权限
- ✅ 安装systemd服务
- ✅ 配置环境变量
- ✅ 运行数据库迁移
- ✅ 启动服务

#### 步骤4: 使用您的实际配置

```bash
# 复制您的生产配置
cp .env.production /www/wwwroot/go-api-solve/.env

# 重启服务使配置生效
systemctl restart go-api-solve
```

### 🔍 验证部署

```bash
# 检查服务状态
systemctl status go-api-solve

# 测试API
curl http://localhost:8080/api/v1/health
```

预期响应：
```json
{
  "status": "ok",
  "message": "Go API Solve service is running",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 🌐 配置宝塔面板反向代理

1. **添加网站**
   - 域名：您的域名
   - 根目录：`/www/wwwroot/go-api-solve`

2. **设置反向代理**
   - 代理名称：`go-api-solve`
   - 目标URL：`http://127.0.0.1:8080`
   - 发送域名：`$host`

3. **开放端口**
   - 在宝塔面板安全设置中开放 `8080` 端口

### 📊 服务管理

```bash
# 启动服务
systemctl start go-api-solve

# 停止服务  
systemctl stop go-api-solve

# 重启服务
systemctl restart go-api-solve

# 查看状态
systemctl status go-api-solve

# 查看日志
journalctl -u go-api-solve -f
```

### 🔧 配置说明

您的配置已经包含在 `.env.production` 文件中：

- **数据库**: `***********:3380` (已配置)
- **Redis**: `r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com:6379` (已配置)
- **API密钥**: Qwen和DeepSeek密钥 (已配置)

### 🚨 故障排除

#### 服务启动失败

```bash
# 查看详细错误
journalctl -u go-api-solve -n 50

# 常见问题：
# 1. 端口被占用 - 检查: netstat -tlnp | grep 8080
# 2. 权限问题 - 检查: ls -la /www/wwwroot/go-api-solve/
# 3. 配置错误 - 检查: cat /www/wwwroot/go-api-solve/.env
```

#### 数据库连接失败

```bash
# 测试数据库连接
mysql -h*********** -P3380 -ugmdns -p5e7fFn3HpPfuQ6Qx42Az solve_api_go
```

#### Redis连接失败

```bash
# 测试Redis连接
redis-cli -h r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com -p 6379 -a EtdDj8xJ385pUPUT ping
```

### 📱 API测试

部署成功后，您可以使用以下API：

```bash
# 健康检查
curl http://your-server-ip:8080/api/v1/health

# 图片处理 (POST请求)
curl -X POST http://your-server-ip:8080/api/v1/process-image \
  -H "Content-Type: application/json" \
  -d '{"image_url": "https://example.com/image.jpg"}'
```

### 🎉 完成！

如果一切正常，您的Go API Solve服务现在应该已经在宝塔面板环境中成功运行了！

---

**需要帮助？**

如果遇到任何问题，请提供：
1. 服务状态：`systemctl status go-api-solve`
2. 错误日志：`journalctl -u go-api-solve -n 100`
3. 配置文件：`cat /www/wwwroot/go-api-solve/.env`
