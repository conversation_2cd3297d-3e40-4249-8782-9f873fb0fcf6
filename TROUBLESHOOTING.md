# Go API Solve - 故障排除指南

## 🚨 数据库连接失败问题

### 问题症状
```
Failed to initialize database: failed to connect to database: dial tcp [::1]:3306: connect: connection refused
```

### 问题原因
应用尝试连接本地MySQL (`localhost:3306`)，但实际数据库在远程服务器上，说明环境变量没有正确加载。

### 🔧 快速修复方案

#### 方案1: 使用修复脚本 (推荐)

```bash
# 运行修复脚本
sudo ./fix_env.sh
```

修复脚本会自动：
- ✅ 停止服务
- ✅ 创建正确的 `.env` 文件
- ✅ 设置正确的文件权限
- ✅ 更新systemd配置
- ✅ 测试数据库连接
- ✅ 重启服务

#### 方案2: 手动修复

```bash
# 1. 停止服务
sudo systemctl stop go-api-solve

# 2. 创建正确的环境变量文件
sudo tee /www/wwwroot/go-api-solve/.env << 'EOF'
SERVER_PORT=8080
GIN_MODE=release
MYSQL_HOST=***********
MYSQL_PORT=3380
MYSQL_USERNAME=gmdns
MYSQL_PASSWORD=5e7fFn3HpPfuQ6Qx42Az
MYSQL_DATABASE=solve_api_go
MYSQL_CHARSET=utf8mb4
REDIS_HOST=r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com
REDIS_PORT=6379
REDIS_USERNAME=r-bp1t323p6w8yn2cpq0
REDIS_PASSWORD=EtdDj8xJ385pUPUT
REDIS_DB=0
QWEN_KEY=sk-3920274bedf642c2b7495f534aadca84
DEEPSEEK_KEY=***********************************
EOF

# 3. 设置权限
sudo chown www:www /www/wwwroot/go-api-solve/.env
sudo chmod 600 /www/wwwroot/go-api-solve/.env

# 4. 重新加载systemd配置
sudo systemctl daemon-reload

# 5. 启动服务
sudo systemctl start go-api-solve
```

### 🔍 验证修复

```bash
# 检查服务状态
systemctl status go-api-solve

# 查看日志
journalctl -u go-api-solve -f

# 测试API
curl http://localhost:8080/api/v1/health
```

## 🔧 其他常见问题

### 1. 端口被占用

**症状**: `bind: address already in use`

**解决方案**:
```bash
# 查看占用8080端口的进程
sudo netstat -tlnp | grep 8080

# 杀死占用进程
sudo kill -9 PID

# 或者修改端口
echo "SERVER_PORT=8081" >> /www/wwwroot/go-api-solve/.env
```

### 2. 权限问题

**症状**: `permission denied`

**解决方案**:
```bash
# 设置正确权限
sudo chown -R www:www /www/wwwroot/go-api-solve
sudo chmod +x /www/wwwroot/go-api-solve/go-api-solve
sudo chmod 600 /www/wwwroot/go-api-solve/.env
```

### 3. 防火墙问题

**症状**: 外部无法访问API

**解决方案**:
```bash
# 开放8080端口
sudo ufw allow 8080/tcp

# 或者使用firewalld
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

### 4. Redis连接失败

**症状**: `redis connection failed`

**解决方案**:
```bash
# 测试Redis连接
redis-cli -h r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com -p 6379 -a EtdDj8xJ385pUPUT ping

# 检查网络连接
telnet r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com 6379
```

## 📊 诊断命令

### 服务状态检查

```bash
# 服务状态
systemctl status go-api-solve

# 详细日志
journalctl -u go-api-solve -n 50

# 实时日志
journalctl -u go-api-solve -f

# 错误日志
journalctl -u go-api-solve --since "1 hour ago" -p err
```

### 配置检查

```bash
# 检查环境变量文件
cat /www/wwwroot/go-api-solve/.env

# 检查文件权限
ls -la /www/wwwroot/go-api-solve/

# 检查进程
ps aux | grep go-api-solve

# 检查端口
netstat -tlnp | grep 8080
```

### 网络连接测试

```bash
# 测试MySQL连接
mysql -h*********** -P3380 -ugmdns -p5e7fFn3HpPfuQ6Qx42Az solve_api_go

# 测试Redis连接
redis-cli -h r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com -p 6379 -a EtdDj8xJ385pUPUT ping

# 测试API
curl -v http://localhost:8080/api/v1/health
```

## 🆘 紧急恢复

如果所有方法都失败，可以尝试完全重新部署：

```bash
# 1. 完全停止服务
sudo systemctl stop go-api-solve
sudo systemctl disable go-api-solve

# 2. 清理旧文件
sudo rm -rf /www/wwwroot/go-api-solve
sudo rm -f /etc/systemd/system/go-api-solve.service

# 3. 重新运行安装脚本
sudo ./install.sh

# 4. 运行修复脚本
sudo ./fix_env.sh
```

## 📞 获取帮助

如果问题仍然存在，请提供以下信息：

1. **系统信息**: `uname -a`
2. **服务状态**: `systemctl status go-api-solve`
3. **完整日志**: `journalctl -u go-api-solve -n 100`
4. **配置文件**: `cat /www/wwwroot/go-api-solve/.env`
5. **网络测试**: MySQL和Redis连接测试结果

---

**重要提醒**: 确保服务器能够访问外部数据库和Redis服务，检查防火墙和网络配置。
